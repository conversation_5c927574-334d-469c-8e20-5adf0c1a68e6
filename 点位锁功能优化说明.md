# 点位锁功能优化说明

## 功能概述

在点位锁页面中，当用户选择点位时，系统会自动调用获取点位详情接口，并根据返回的点位详情字段来动态显示相应的值输入组件。

## 实现的功能

### 1. 动态值输入组件

根据点位详情中的 `valueType` 字段，系统会显示不同的输入组件：

- **valueType = "1" (数值类型)**：显示数字输入框 (`el-input-number`)
  - 支持最小值和最大值限制 (`valueMin`, `valueMax`)
  - 显示取值范围提示
  - 条件符号支持：==、!=、>、<、>=、<=

- **valueType = "2" (选项类型)**：显示下拉选择框 (`el-select`)
  - 解析 `valueOption` 字段中的JSON数据
  - 显示可选项列表
  - 条件符号仅支持：==、!=（因为选项类型不适合大小比较）

- **其他类型**：显示普通文本输入框 (`el-input`)
  - 条件符号支持：==、!=、>、<、>=、<=

### 2. 点位详情获取

当用户选择触发点位或被锁点位时，系统会：

1. 调用 `getDevicePoint(pointId)` 接口获取点位详情
2. 将详情数据存储在 `triggerPointDetail` 或 `lockPointDetail` 中
3. 根据详情数据动态渲染输入组件

### 3. 数据结构示例

```javascript
// 点位详情数据结构
{
  "pointId": "67",
  "pointName": "手动点V.主机2组冷却泵使用4",
  "valueType": "2",
  "valueOption": "[{\"name\":\"开\",\"value\":\"1\"},{\"name\":\"关\",\"value\":\"0\"}]",
  "valueMax": null,
  "valueMin": null,
  "controlValue": null,
  "unit": null,
  "clientAccess": "R/W"
}
```

## 修改的文件

### 主要修改点

1. **导入API接口**
   - 添加了 `getDevicePoint` 接口的导入

2. **数据属性**
   - 添加 `triggerPointDetail` 和 `lockPointDetail` 用于存储点位详情

3. **模板修改**
   - 触发值输入：根据 `triggerPointDetail.valueType` 动态显示组件
   - 被锁值输入：根据 `lockPointDetail.valueType` 动态显示组件
   - 添加点位选择变化的事件处理

4. **方法添加**
   - `handleTriggerPointChange()`: 处理触发点位变化，自动清理不适用的条件符号
   - `handleLockPointChange()`: 处理被锁点位变化，自动清理不适用的条件符号
   - `parseValueOptions()`: 解析选项值JSON数据

5. **智能条件符号过滤**
   - 根据点位类型动态显示可用的条件符号
   - 选项类型点位自动隐藏大小比较符号
   - 点位类型变化时自动清理不适用的已选条件符号

5. **现有方法修改**
   - `handleTriggerDeviceChange()`: 清空点位详情和值
   - `handleLockDeviceChange()`: 清空点位详情和值
   - `reset()`: 清空点位详情
   - `handleUpdate()`: 编辑时获取点位详情

## 使用流程

1. **新增互锁规则**
   - 选择触发设备 → 选择触发点位 → 自动获取点位详情 → 根据类型显示相应输入组件
   - 选择被锁设备 → 选择被锁点位 → 自动获取点位详情 → 根据类型显示相应输入组件

2. **编辑互锁规则**
   - 系统自动加载已选择点位的详情信息
   - 根据点位类型显示相应的输入组件

## 优势

1. **用户体验优化**：根据点位类型自动显示合适的输入组件
2. **数据准确性**：数值类型支持范围限制，选项类型提供下拉选择
3. **操作便捷性**：选项类型无需手动输入，减少错误
4. **界面一致性**：与设备列表页面的控制逻辑保持一致

## 测试要点

1. **数值类型点位测试**
   - 验证数字输入框和范围限制
   - 验证所有条件符号（==、!=、>、<、>=、<=）都可选择

2. **选项类型点位测试**
   - 验证下拉选择框和选项显示
   - 验证只显示等于和不等于条件符号
   - 验证从数值类型切换到选项类型时，大小比较符号被自动清空

3. **点位类型切换测试**
   - 选择数值类型点位后选择大于条件，再切换到选项类型点位，验证条件符号被清空
   - 选择选项类型点位后选择等于条件，再切换到数值类型点位，验证条件符号保持不变

4. **编辑功能测试**
   - 编辑现有互锁规则，验证点位详情正确加载
   - 验证条件符号根据点位类型正确显示

5. **数据清理测试**
   - 验证设备变化时点位详情正确清空
   - 验证表单重置时点位详情正确清空

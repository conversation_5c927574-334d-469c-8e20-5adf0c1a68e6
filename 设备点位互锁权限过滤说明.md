# 设备点位互锁权限过滤说明

## 修改概述

为了确保设备点位互锁功能只操作可读写的设备和点位，已在所有相关的API调用中添加了 `clientAccess: 'R/W'` 过滤条件。

## 修改详情

### 1. 设备选项查询
```javascript
// 获取设备选项时过滤可读写设备
getDeviceOptions() {
  deviceOption({ clientAccess: 'R/W' }).then(response => {
    this.deviceOptions = response.data || [];
  });
}
```

### 2. 点位选项查询
```javascript
// 获取点位选项时过滤可读写点位
getPointOptions() {
  devicePointOption({ clientAccess: 'R/W' }).then(response => {
    this.pointOptions = response.data || [];
  });
}
```

### 3. 设备变化时的点位加载
```javascript
// 触发设备变化时，获取对应的可读写点位
handleTriggerDeviceChange(deviceId) {
  if (deviceId) {
    devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
      this.triggerPointOptions = response.data || [];
    });
  }
}

// 被锁设备变化时，获取对应的可读写点位
handleLockDeviceChange(deviceId) {
  if (deviceId) {
    devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
      this.lockPointOptions = response.data || [];
    });
  }
}
```

### 4. 查询条件中的设备点位联动
```javascript
// 查询条件中设备变化时，获取对应的可读写点位
handleQueryDeviceChange(deviceId) {
  if (deviceId) {
    devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
      this.queryPointOptions = response.data || [];
    });
  }
}

// 查询条件中被锁设备变化时，获取对应的可读写点位
handleQueryLockDeviceChange(deviceId) {
  if (deviceId) {
    devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
      this.queryLockPointOptions = response.data || [];
    });
  }
}
```

### 5. 编辑时的数据加载
```javascript
// 修改操作时，加载对应设备的可读写点位
if (this.form.deviceId) {
  const triggerPromise = devicePointOption({ 
    deviceId: this.form.deviceId, 
    clientAccess: 'R/W' 
  }).then(response => {
    this.triggerPointOptions = response.data || [];
  });
}

if (this.form.lockDeviceId) {
  const lockPromise = devicePointOption({ 
    deviceId: this.form.lockDeviceId, 
    clientAccess: 'R/W' 
  }).then(response => {
    this.lockPointOptions = response.data || [];
  });
}
```

## 影响范围

### 涉及的API调用
1. `deviceOption({ clientAccess: 'R/W' })` - 设备选项查询
2. `devicePointOption({ clientAccess: 'R/W' })` - 点位选项查询
3. `devicePointOption({ deviceId, clientAccess: 'R/W' })` - 特定设备的点位查询

### 涉及的功能模块
1. 页面初始化时的数据加载
2. 新增互锁配置时的设备点位选择
3. 修改互锁配置时的数据回显
4. 查询条件中的设备点位联动

## 业务意义

### 1. 安全性保障
- 确保只有具有读写权限的设备和点位才能参与互锁配置
- 防止对只读设备或点位进行误操作

### 2. 功能完整性
- 互锁功能需要对设备点位进行控制操作
- 只有可读写的点位才能被有效控制

### 3. 用户体验
- 用户只看到可操作的设备和点位选项
- 避免配置无效的互锁规则

## 测试验证

### 1. 功能测试
- 验证设备选项只显示可读写设备
- 验证点位选项只显示可读写点位
- 验证设备变化时点位联动正确

### 2. 权限测试
- 验证只读设备不出现在选项中
- 验证只读点位不出现在选项中
- 验证混合权限设备的点位过滤正确

### 3. 兼容性测试
- 验证现有互锁配置的正常显示
- 验证编辑功能的数据加载正确
- 验证查询功能的过滤效果

## 注意事项

1. **API兼容性**：确保后端API支持 `clientAccess` 参数过滤
2. **数据一致性**：确保所有相关查询都使用相同的过滤条件
3. **错误处理**：保持原有的错误处理机制
4. **性能影响**：过滤条件可能影响查询性能，需要监控

## 后续优化建议

1. **缓存机制**：考虑对设备和点位选项进行缓存
2. **批量查询**：优化多次API调用的性能
3. **权限提示**：在界面上提示当前过滤条件
4. **配置化**：将权限过滤条件配置化，便于后续调整

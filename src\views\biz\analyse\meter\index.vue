<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <el-col style="margin-bottom: 10px">
          <el-radio v-for="dict in dict.type.energy_type" :key="dict.value"
                    :label="dict.value"
                    v-model="energyType"
                    @input="selectType"
          >{{ dict.label }}
          </el-radio>
        </el-col>
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入区域名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="meterTreeOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--能耗数据-->
      <el-col :span="20" :xs="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #actions>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </template>
        </time-analysis-selector>

        <!-- 能耗柱状图 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="chart-container energy-chart">
              <div class="chart-header">
                <span class="chart-title">能耗趋势分析</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('energy')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('energyBarChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="energyBarChart" class="chart-content"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 同比环比折线图 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">同比环比趋势图</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('compare')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('compareLineChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="compareLineChart" class="chart-content"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 数据视图对话框 -->
        <el-dialog :title="dataViewTitle" :visible.sync="dataViewVisible" width="50%">
          <el-table :data="dataViewData" border style="width: 100%">
            <el-table-column prop="date" label="日期"></el-table-column>
            <el-table-column prop="energy" label="能耗值"></el-table-column>
            <el-table-column prop="qoq" label="环比变化 (%)">
              <template slot-scope="scope">
                <span :style="{color: scope.row.qoq >= 0 ? 'red' : 'green'}">
                  {{ scope.row.qoq >= 0 ? '+' + scope.row.qoq : scope.row.qoq }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="yoy" label="同比变化 (%)">
              <template slot-scope="scope">
                <span :style="{color: scope.row.yoy >= 0 ? 'red' : 'green'}">
                  {{ scope.row.yoy >= 0 ? '+' + scope.row.yoy : scope.row.yoy }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector'
import {meterEnergyAnalyse} from '@/api/biz/analyse'
import {meterTree} from '@/api/biz/meter'

export default {
  name: 'analyseMeter',
  components: {
    TimeAnalysisSelector
  },
  dicts: [ 'energy_type'],
  data() {
    return {
      // 能源类型
      energyType: '1',
      // 区域名称
      areaName: undefined,
      meterTreeOptions: [],
      defaultProps: {
        children: "children",
        label: "name"
      },
      // 查询参数
      queryParams: {
        meterId: undefined,
        analysisType: undefined,
        startTime: undefined,
        endTime: undefined,
        pointType: undefined,
      },
      // 图表对象
      energyBarChart: null,
      compareLineChart: null,
      // loading对象
      energyBarChartLoading: null,
      compareLineChartLoading: null,
      // 能耗数据
      energyData: {
        times: [],
        thisEnergy: [],
        qoqEnergy: [],
        yoyEnergy: [],
        qoqPercentage: [], // 环比百分比
        yoyPercentage: []  // 同比百分比
      },
      // 数据视图
      dataViewVisible: false,
      dataViewTitle: '能耗数据详情',
      dataViewData: [],
      // 图表颜色
      chartColors: {
        energy: ['#83bff6', '#188df0', '#188df0'],
        qoq: '#91CC75',
        yoy: '#EE6666',
        pie: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272']
      },
    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.selectType(this.type)
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.resizeCharts)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    selectType(data) {
      this.getMeterTree()
    },
    // 能耗分析
    getEnergyAnalyse() {
      // 显示能耗柱状图的loading
      this.energyBarChartLoading = this.$loading({
        target: this.$refs.energyBarChart,
        text: '数据加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });

      // 显示同比环比折线图的loading
      this.compareLineChartLoading = this.$loading({
        target: this.$refs.compareLineChart,
        text: '数据加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });

      meterEnergyAnalyse(this.queryParams).then(res => {
        if (res.data) {
          const {times, thisEnergy, qoqEnergy, yoyEnergy} = res.data
          this.energyData.times = times
          this.energyData.thisEnergy = thisEnergy
          this.energyData.qoqEnergy = qoqEnergy
          this.energyData.yoyEnergy = yoyEnergy

          // 计算环比百分比
          this.energyData.qoqPercentage = this.calculatePercentage(thisEnergy, qoqEnergy)

          // 计算同比百分比
          this.energyData.yoyPercentage = this.calculatePercentage(thisEnergy, yoyEnergy)

          // 初始化图表
          this.initCharts()

          // 准备数据视图数据
          this.prepareDataViewData()
        }

        // 关闭loading
        if (this.energyBarChartLoading) {
          this.energyBarChartLoading.close();
        }
        if (this.compareLineChartLoading) {
          this.compareLineChartLoading.close();
        }
      }).catch(error => {
        console.error('获取能耗分析数据失败:', error);
        this.$message.error('获取能耗分析数据失败');

        // 关闭loading
        if (this.energyBarChartLoading) {
          this.energyBarChartLoading.close();
        }
        if (this.compareLineChartLoading) {
          this.compareLineChartLoading.close();
        }
      })
    },
    // 计算百分比
    calculatePercentage(current, compare) {
      return current.map((value, index) => {
        if (!compare[index] || compare[index] === 0) {
          return 0
        }
        // 计算百分比变化: (当前值 - 对比值) / 对比值 * 100
        return parseFloat(((value - compare[index]) / compare[index] * 100).toFixed(2))
      })
    },
    // 初始化所有图表
    initCharts() {
      this.initEnergyBarChart()
      this.initCompareLineChart()
    },
    // 初始化能耗柱状图
    initEnergyBarChart() {
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
      }
      this.energyBarChart = echarts.init(this.$refs.energyBarChart, 'macarons')

      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: (params) => {
            return `<div style="font-weight:bold">${params[0].axisValue}</div>
                   <div style="margin-top:5px">
                     <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params[0].color}"></span>
                     能耗值: ${params[0].value.toFixed(2)}
                   </div>`
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        dataZoom: [],
        xAxis: {
          type: 'category',
          data: this.energyData.times,
          boundaryGap: true,
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理
              if (!value) return '';

              try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                  // 如果是无效日期，直接返回原始值
                  return value;
                }

                // 根据日期字符串长度判断数据类型
                if (value.includes('-') || value.includes('/')) {
                  // 包含日期分隔符的情况
                  if (value.includes(':') || value.includes(' ')) {
                    // 小时格式 (HOURLY)
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
                  } else if (value.length <= 7) {
                    // 月份格式 (MONTHLY)
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                  } else {
                    // 日期格式 (DAILY)
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }
                } else if (value.length === 4) {
                  // 年份格式 (YEARLY)
                  return value;
                } else {
                  // 周格式 (WEEK) 或其他
                  return value;
                }
              } catch (e) {
                // 发生异常时返回原始值
                console.error('Date formatting error:', e);
                return value;
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '能耗值',
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '能耗数值',
            type: 'bar',
            barWidth: '60%',
            data: this.energyData.thisEnergy,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#83bff6'},
                {offset: 0.5, color: '#188df0'},
                {offset: 1, color: '#188df0'}
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {offset: 0, color: '#2378f7'},
                  {offset: 0.7, color: '#2378f7'},
                  {offset: 1, color: '#83bff6'}
                ])
              }
            },
            markLine: {
              symbol: 'none',
              lineStyle: {
                color: '#5470C6',
                type: 'dashed'
              },
              data: [
                {
                  type: 'average',
                  name: '平均值',
                  label: {
                    position: 'middle',
                    formatter: (params) => {
                      return `平均: ${params.value.toFixed(2)}`
                    },
                    fontSize: 10,
                    color: '#5470C6',
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    padding: [2, 4],
                    borderRadius: 2,
                    distance: 10
                  }
                }
              ]
            }
          }
        ]
      }

      this.energyBarChart.setOption(option)
    },
    // 初始化同比环比折线图
    initCompareLineChart() {
      if (this.compareLineChart) {
        this.compareLineChart.dispose()
      }
      this.compareLineChart = echarts.init(this.$refs.compareLineChart, 'macarons')

      const option = {
        title: {
          text: '同比环比分析',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              const value = param.value
              const sign = value >= 0 ? '+' : ''
              const color = value >= 0 ? 'red' : 'green'

              result += `<div style="margin: 3px 0">
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: <span style="color:${color}">${sign}${value}%</span>
              </div>`
            }

            return result
          }
        },
        legend: {
          data: ['环比变化', '同比变化'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '30px',
          top: '60px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.energyData.times,
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理
              if (!value) return '';

              try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                  // 如果是无效日期，直接返回原始值
                  return value;
                }

                // 根据日期字符串长度判断数据类型
                if (value.includes('-') || value.includes('/')) {
                  // 包含日期分隔符的情况
                  if (value.includes(':') || value.includes(' ')) {
                    // 小时格式 (HOURLY)
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
                  } else if (value.length <= 7) {
                    // 月份格式 (MONTHLY)
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                  } else {
                    // 日期格式 (DAILY)
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }
                } else if (value.length === 4) {
                  // 年份格式 (YEARLY)
                  return value;
                } else {
                  // 周格式 (WEEK) 或其他
                  return value;
                }
              } catch (e) {
                // 发生异常时返回原始值
                console.error('Date formatting error:', e);
                return value;
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '百分比',
          axisLabel: {
            formatter: '{value} %'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '环比变化',
            type: 'line',
            data: this.energyData.qoqPercentage,
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: this.chartColors.qoq
            },
            itemStyle: {
              color: this.chartColors.qoq,
              borderWidth: 2
            },
            markLine: {
              data: []
            },
            areaStyle: {
              opacity: 0.1,
              color: this.chartColors.qoq
            }
          },
          {
            name: '同比变化',
            type: 'line',
            data: this.energyData.yoyPercentage,
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: this.chartColors.yoy
            },
            itemStyle: {
              color: this.chartColors.yoy,
              borderWidth: 2
            },
            markLine: {
              data: []
            },
            areaStyle: {
              opacity: 0.1,
              color: this.chartColors.yoy
            }
          }
        ]
      }

      this.compareLineChart.setOption(option)
    },

    // 调整所有图表大小
    resizeCharts() {
      if (this.energyBarChart) {
        this.energyBarChart.resize()
      }
      if (this.compareLineChart) {
        this.compareLineChart.resize()
      }
    },
    // 销毁所有图表
    disposeCharts() {
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
        this.energyBarChart = null
      }
      if (this.compareLineChart) {
        this.compareLineChart.dispose()
        this.compareLineChart = null
      }
    },
    // 准备数据视图数据
    prepareDataViewData() {
      this.dataViewData = this.energyData.times.map((time, index) => {
        return {
          date: time,
          energy: this.energyData.thisEnergy[index],
          qoq: this.energyData.qoqPercentage[index],
          yoy: this.energyData.yoyPercentage[index]
        }
      })
    },
    // 显示数据视图
    showDataView(type) {
      switch (type) {
        case 'energy':
          this.dataViewTitle = '能耗数据详情'
          break
        case 'compare':
          this.dataViewTitle = '同比环比数据详情'
          break
        case 'yearCompare':
          this.dataViewTitle = '年度能耗对比详情'
          break
        default:
          this.dataViewTitle = '能耗数据详情'
      }
      this.dataViewVisible = true
    },
    // 保存图表为图片
    saveAsImage(chartRef) {
      let chart = null
      switch (chartRef) {
        case 'energyBarChart':
          chart = this.energyBarChart
          break
        case 'compareLineChart':
          chart = this.compareLineChart
          break
      }

      if (chart) {
        const url = chart.getDataURL({
          pixelRatio: 2,
          backgroundColor: '#fff'
        })

        const link = document.createElement('a')
        link.download = `能源分析_${chartRef}_${new Date().getTime()}.png`
        link.href = url
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },
    // 获取区域表具树结构
    getMeterTree() {
      meterTree({
        energyType: this.energyType
      }).then(res => {
        this.meterTreeOptions = res.data;
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      // 如果是区域节点,不查询,直接返回
      if (data.type === "area") {
        return;
      }
      this.queryParams.meterId = data.id;
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 检查必要参数
      if (!this.queryParams.meterId || !this.queryParams.analysisType ||
        !this.queryParams.startTime || !this.queryParams.endTime ) {
        this.$message.warning('请选择表具、分析方式和时间范围');
        return;
      }
      this.getEnergyAnalyse();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset();
      // 重置其他参数
      this.queryParams.meterId = undefined;
      this.$refs.tree.setCurrentKey(null);

      // 关闭所有loading（如果有）
      if (this.energyBarChartLoading) {
        this.energyBarChartLoading.close();
      }
      if (this.compareLineChartLoading) {
        this.compareLineChartLoading.close();
      }
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType;
      this.queryParams.startTime = params.startTime;
      this.queryParams.endTime = params.endTime;
    },
  }
}
</script>
<style scoped>
::v-deep .el-radio__original {
  display: none !important;
}

::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
.el-radio__inner {
  box-shadow: none !important;
}

.chart-container {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 15px;
}

.chart-tools i {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
}

.chart-tools i:hover {
  color: #409EFF;
}

.chart-content {
  width: 100%;
  height: 280px;
  position: relative;
}

.energy-chart {
  padding-top: 10px;
  padding-bottom: 20px;
}
</style>

# 设备点位互锁功能优化说明

## 字段结构更新

根据后端字段结构的更新，前端界面已进行相应优化，新增了被锁设备的条件设置功能。

### 新的字段映射

```java
// 触发设备相关
private String deviceId;        // 设备id
private String pointId;         // 点位id
private String value;           // 触发值
private String symbol;          // 触发符号

// 被锁设备相关
private String lockDeviceId;    // 被锁设备id
private String lockPointId;     // 被锁点位id
private String lockValue;       // 被锁值
private String lockSymbol;      // 被锁符号

// 其他信息
private String hint;            // 提示
private String remark;          // 备注
```

## 界面结构优化

### 新的四层布局结构

#### 第一层：设备和点位选择
- **左侧**：触发设备选择
  - 触发设备选择
  - 触发点位选择
- **右侧**：被锁设备选择
  - 被锁设备选择
  - 被锁点位选择

#### 第二层：触发条件设置
- 触发条件符号选择（==、!=、>、<、>=、<=）
- 触发值输入

#### 第三层：被锁条件设置（新增）
- 被锁条件符号选择（==、!=、>、<、>=、<=）
- 被锁值输入

#### 第四层：提示信息和备注
- 提示信息输入
- 备注信息输入

## 表格显示优化

### 新的表格列结构
```vue
<el-table-column label="触发设备" align="center" prop="deviceName" />
<el-table-column label="触发点位" align="center" prop="pointName" />
<el-table-column label="触发条件" align="center" width="120">
  <template slot-scope="scope">
    <span>{{ scope.row.symbol }} {{ scope.row.value }}</span>
  </template>
</el-table-column>
<el-table-column label="被锁设备" align="center" prop="lockDeviceName" />
<el-table-column label="被锁点位" align="center" prop="lockPointName" />
<el-table-column label="被锁条件" align="center" width="120">
  <template slot-scope="scope">
    <span>{{ scope.row.lockSymbol }} {{ scope.row.lockValue }}</span>
  </template>
</el-table-column>
```

## 表单验证优化

### 新增的验证规则
```javascript
// 触发条件表单校验
conditionRules: {
  symbol: [
    { required: true, message: "触发条件不能为空", trigger: "change" }
  ],
  value: [
    { required: true, message: "触发值不能为空", trigger: "blur" }
  ]
},
// 被锁条件表单校验（新增）
lockConditionRules: {
  lockSymbol: [
    { required: true, message: "被锁条件不能为空", trigger: "change" }
  ],
  lockValue: [
    { required: true, message: "被锁值不能为空", trigger: "blur" }
  ]
}
```

### 表单验证流程
```javascript
submitForm() {
  // 验证所有表单
  Promise.all([
    triggerForm.validate(),      // 触发设备选择验证
    lockForm.validate(),         // 被锁设备选择验证
    conditionForm.validate(),    // 触发条件验证
    lockConditionForm.validate(), // 被锁条件验证（新增）
    infoForm.validate()          // 提示信息验证
  ]).then(() => {
    // 提交数据
  });
}
```

## 功能增强

### 1. 更精确的互锁控制
- **触发条件**：当触发设备的点位满足指定条件时
- **被锁条件**：将被锁设备的点位设置为指定条件

### 2. 灵活的条件设置
- 支持多种比较符号（==、!=、>、<、>=、<=）
- 触发条件和被锁条件可以独立设置
- 支持不同的数值类型

### 3. 完整的业务逻辑
```
当 [触发设备]-[触发点位] [触发符号] [触发值] 时，
将 [被锁设备]-[被锁点位] 设置为 [被锁符号] [被锁值]
```

## 使用示例

### 示例1：温度保护
- **触发条件**：温度传感器 > 80°C
- **被锁条件**：加热器开关 = 0（关闭）
- **说明**：当温度超过80度时，自动关闭加热器

### 示例2：压力联锁
- **触发条件**：压力传感器 >= 10bar
- **被锁条件**：安全阀 = 1（开启）
- **说明**：当压力达到10bar时，自动开启安全阀

### 示例3：设备状态联锁
- **触发条件**：主设备状态 != 1（非运行状态）
- **被锁条件**：从设备状态 = 0（停止）
- **说明**：当主设备不在运行状态时，从设备自动停止

## 数据结构对比

### 优化前
```javascript
{
  deviceId: "触发设备ID",
  pointId: "触发点位ID", 
  lockDeviceId: "被锁设备ID",
  lockPointId: "被锁点位ID",
  lockValue: "互锁值",  // 既是触发值又是被锁值
  symbol: "符号"       // 只有一个符号
}
```

### 优化后
```javascript
{
  deviceId: "触发设备ID",
  pointId: "触发点位ID",
  value: "触发值",           // 明确的触发值
  symbol: "触发符号",        // 明确的触发符号
  lockDeviceId: "被锁设备ID",
  lockPointId: "被锁点位ID", 
  lockValue: "被锁值",       // 明确的被锁值
  lockSymbol: "被锁符号"     // 明确的被锁符号
}
```

## 兼容性说明

1. **数据迁移**：需要将原有的 `lockValue` 数据迁移到新的字段结构
2. **API适配**：后端API需要支持新的字段结构
3. **权限控制**：继续使用 `clientAccess: 'R/W'` 过滤条件

## 测试要点

1. **字段映射测试**：验证新字段的正确保存和显示
2. **条件逻辑测试**：验证触发条件和被锁条件的独立性
3. **表单验证测试**：验证所有表单的验证规则
4. **数据兼容测试**：验证与现有数据的兼容性
5. **权限过滤测试**：验证设备和点位的权限过滤功能
